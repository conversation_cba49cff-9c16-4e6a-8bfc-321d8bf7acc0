import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON>aEye, FaEyeSlash } from "react-icons/fa"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { loginStrings } from "../../utils/strings/login-strings"
import { LocalStorageService, Keys } from "../../utils/local-storage"
import keys from "../../utils/strings/query-keys"
import authEndpoints from "../../api/service/auth"
import { useApiMutation } from "../../hooks/react-query-hooks"
import { useAppDispatch } from "../../store/store"
import ICON from "../../assets/icons/icon.png"

// Zod validation schema
const loginSchema = z.object({
  email_address: z
    .string()
    .min(1, { message: loginStrings.emailRequired })
    .email({ message: loginStrings.invalidEmail }),
  password: z
    .string()
    .min(1, { message: loginStrings.passwordRequired })
    .min(8, { message: loginStrings.passwordMinLength }),
})

type LoginForm = z.infer<typeof loginSchema>

function Login() {
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()
  const dispatch = useAppDispatch()

  // Initialize react-hook-form with zod validation
  const form = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email_address: "",
      password: "",
    },
  })

  const { isPending, mutate } = useApiMutation({
    queryKey: [keys.login],
    mutationFn: authEndpoints.login,
    onSuccessHandler: (response: { message: string; email_address: string; refreshToken?: string }) => {
      if (response.message === "OTP Verification Required.") {
        LocalStorageService.setItem(Keys.UserName, response.email_address)
        navigate("/auth/email-verification")
        toast.info(loginStrings.loginSuccessful, {
          description: "OTP verification pending.",
        })
      } else {
        const { email_address, refreshToken } = response
        LocalStorageService.setItem(Keys.Token, refreshToken as string)
        LocalStorageService.setItem(Keys.UserName, email_address)

        navigate("/auth/choose-profile")
        toast.success("Login successful!", {
          description: "Welcome back!",
        })
      }
    },
    onError: (msg: string | null) => {
      toast.error(loginStrings.loginFailed, {
        description: msg ?? "Something went wrong",
      })
    },
  })

  const onSubmit = (data: LoginForm) => {
    mutate(data)
  }

  return (
    <Card className="w-full max-w-md mx-auto shadow-xl border-0 bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
      <CardHeader className="flex flex-col items-center space-y-4 pb-8">
       
          <img src={ICON} alt="Logo" className="w-12 h-12" />
        
        <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          {loginStrings.login}
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
          Welcome back! Please sign in to your account
        </p>
      </CardHeader>

      <CardContent className="px-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Field */}
            <FormField
              control={form.control}
              name="email_address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {loginStrings.email}<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email address"
                      className="h-12 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-xs text-red-600 dark:text-red-400 font-medium" />
                </FormItem>
              )}
            />

            {/* Password Field */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {loginStrings.password}<span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        className="h-12 pr-12 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 placeholder:text-gray-500 dark:placeholder:text-gray-400"
                        {...field}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                      >
                        {showPassword ? (
                          <FaEyeSlash className="w-5 h-5" />
                        ) : (
                          <FaEye className="w-5 h-5" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage className="text-xs text-red-600 dark:text-red-400 font-medium" />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
              disabled={isPending}
            >
              {isPending ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Signing in...
                </div>
              ) : (
                loginStrings.login
              )}
            </Button>
          </form>
        </Form>
      </CardContent>

      <CardFooter className="flex flex-col gap-4 px-8 pb-8">
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {loginStrings.dontHaveAccount}{" "}
            <button
              onClick={() => navigate("/auth/register")}
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium hover:underline transition-colors"
            >
              {loginStrings.signUp}
            </button>
          </p>
        </div>
      </CardFooter>
    </Card>
  )
}

export default Login
